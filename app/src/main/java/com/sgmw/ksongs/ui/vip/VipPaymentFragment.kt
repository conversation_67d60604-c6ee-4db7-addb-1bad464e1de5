package com.sgmw.ksongs.ui.vip

import android.animation.ObjectAnimator
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.activity.OnBackPressedCallback
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.SizeUtils
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.GlideUtil
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.constant.Constant
import com.sgmw.ksongs.databinding.FragmentVipPaymentBinding
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.track.ChannelType
import com.sgmw.ksongs.ui.adapter.VipPaymentAdapter
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.viewmodel.vip.VipPaymentViewModel
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager
import com.sgmw.ksongs.widget.SpaceItemDecoration
import com.tme.ktv.demo.bean.VipProductBean

class VipPaymentFragment : BaseFrameFragment<FragmentVipPaymentBinding, VipPaymentViewModel>() {

    private val TAG = "VipPaymentFragment"
    private val mHandler: Handler = Handler(Looper.getMainLooper())
    private val reloadTimes: Long = 30 * 60 * 1000
    private var mSongInfo: SongInfoBean? = null
    private var isShowPlayPage = false
    private var channelType: ChannelType? = null
    private var actionType: Int = -1
    private var needReplay: Boolean = false
    private var needShowPlaying: Boolean = true

    private val mRunnable: Runnable = Runnable {
        loadVipProduct()
    }

    private fun loadVipProduct() {
        mViewModel?.getVipProduct()
        mBinding?.let {
            showQRLoading(it)
        }
    }

    val mAdapter: VipPaymentAdapter by lazy {
        VipPaymentAdapter()
    }

    override fun FragmentVipPaymentBinding.initView() {
        mBinding?.let { binding ->
            binding.ivBack.setOnSingleClickListener {
                goBack()
            }
            val layoutManager =
                AccessibilityLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            binding.recyclerview.layoutManager = layoutManager
            binding.recyclerview.adapter = mAdapter
            binding.recyclerview.addItemDecoration(
                SpaceItemDecoration(
                    bottomSpace = SizeUtils.dp2px(32.0F)
                )
            )

            mAdapter.setOnItemClickListener { adapter, v, position ->
                showQRLoading(binding)
                mAdapter.setSelectPosition(position)
                val goodsInfo = adapter.data[position] as VipProductBean.GoodsInfo
                showQRImageView(goodsInfo)
            }

            binding.layoutLoadStatus.setOnSingleClickListener {
                mViewModel?.getVipProduct()
                mViewModel?.getUserInfo()
                showQRLoading(binding)
            }
        }
    }

    override fun initRequestData() {
        mViewModel?.let {
            it.getUserInfo()
            it.getVipProduct()
            mBinding?.let {
                showQRLoading(it)
            }

        }
    }

    override fun initObserve() {
        super.initObserve()
        mBinding?.let { binding ->
            mViewModel?.let { viewModel ->
                viewModel.mUserInfoBean.observe(viewLifecycleOwner) { userInfo ->
                    userInfo?.user_avatar?.let { it ->
                        GlideUtil.loadRoundCornerImage(
                            requireContext(), it,
                            binding.ivAvatar, 90, R.mipmap.ksongs_useravator_default, R.mipmap.ksongs_useravator_default
                        )
                    }
                    binding.tvUserName.text = userInfo?.user_nick
                }
                viewModel.mVipProductBean.observe(viewLifecycleOwner) {
                    if (it == null) {
                        mBinding?.let {
                            showQRFailed(it)
                        }
                        return@observe
                    }
                    it?.goods_list?.let {
                        if (binding.recyclerview.adapter == null) {
                            mAdapter.setNewInstance(it)
                            showQRImageView(it[0])
                        } else {
                            mAdapter.setList(it)
                            showQRImageView(it[mAdapter.getSelectPosition()])
                        }
                    }

                }
            }
        }

    }

    private fun showQRImageView(currGoods: VipProductBean.GoodsInfo) {
        //添加二维码半小时刷新逻辑
        mHandler.removeCallbacks(mRunnable)
        mBinding?.let {
            it.tvPrice.text = "¥${currGoods.price}"
            val url = currGoods.pay_url
            if (!url.isNullOrEmpty()) {
                try {
                    mHandler.postDelayed(mRunnable, reloadTimes)
                    it.ivPaymentQr.a(url)
                    hideQRLoading()
                } catch (e: Exception) {
                    Log.d(TAG, "showQRImageView ${e.message}")
                    showQRFailed(it)
                }

            } else {
                showQRFailed(it)
            }
        }

    }

    private fun hideQRLoading() {
        mBinding?.let {
            stopAnimation()
            it.layoutLoadStatus.visibility = View.GONE
        }
    }

    private fun showQRLoading(binding: FragmentVipPaymentBinding) {
        binding.layoutLoadStatus.visibility = View.VISIBLE
        binding.ivLoadStatus.setImageResource(R.mipmap.ksongs_qrcode_loading)
        startAnimation()
        binding.tvLoadStatus.visibility = View.GONE
    }

    private fun showQRFailed(binding: FragmentVipPaymentBinding) {
        binding.layoutLoadStatus.visibility = View.VISIBLE
        stopAnimation()
        binding.ivLoadStatus.setImageResource(R.mipmap.ksongs_qrcode_refresh)
        binding.tvLoadStatus.text = requireContext().resources.getText(R.string.qr_network_failed)
    }

    private var mRotateAnimation: ObjectAnimator? = null
    private fun startAnimation() {
        if (mRotateAnimation == null) {
            mRotateAnimation =
                ObjectAnimator.ofFloat<View>(mBinding?.ivLoadStatus, View.ROTATION, 0f, 360f)
            mRotateAnimation?.setDuration(1000)
            mRotateAnimation?.setRepeatCount(ObjectAnimator.INFINITE)
            mRotateAnimation?.setInterpolator(LinearInterpolator())
        }
        if (mRotateAnimation?.isStarted() == false) {
            mRotateAnimation?.start()
        }
    }

    private fun stopAnimation() {
        mRotateAnimation?.cancel()
        // 重置旋转角度
        mBinding?.ivLoadStatus?.rotation = 0f
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mHandler.removeCallbacks(mRunnable)
    }

    private fun getArgumentsData() {
        mSongInfo = arguments?.getParcelable(Constant.KEY_SONG_INFO)
        isShowPlayPage = arguments?.getBoolean(Constant.KEY_PLAY_PAGE_STATE, false) == true
        val ordinal = arguments?.getInt(Constant.KEY_CHANNEL_TYPE, 0) ?: 0
        channelType = ChannelType.values().getOrNull(ordinal)
        actionType = arguments?.getInt(Constant.KEY_ACTION_TYPE, -1) ?: -1
        needReplay = arguments?.getBoolean(Constant.KEY_NEED_REPLAY, false) == true
        needShowPlaying = arguments?.getBoolean(Constant.KEY_NEED_SHOW_PLAYING, true) == true
        Log.d(
            TAG,
            "getArgumentsData songInfo == $mSongInfo = isShowPlayPage =$isShowPlayPage actionType = $actionType   needReplay = $needReplay"
        )
    }

    private fun goBack() {
        getArgumentsData()
        // 添加回调机制，确保在VIP状态检查完成后再执行页面返回
        KaraokePlayerManager.vipBackPlayInfo(
            requireActivity(),
            mSongInfo,
            isShowPlayPage,
            channelType,
            actionType,
            needReplay,
            needShowPlaying
        ) {
            // VIP状态检查完成后的回调，执行页面返回
            if (isAdded && !isDetached) {
                val success = NavigationUtils.popBackStackSafely(findNavController())
                Log.d(TAG, "goBack popBackStack result: $success, current fragment: ${NavigationUtils.getCurrentFragmentTag()}")
            } else {
                Log.w(TAG, "goBack: Fragment is not in valid state, isAdded: $isAdded, isDetached: $isDetached")
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // 获取进入VipPaymentFragment时的状态参数
                getArgumentsData()

                val mainActivity = requireActivity() as? com.sgmw.ksongs.MainActivity
                val isSongPlayControllerShowing = mainActivity?.isSongPlayControllerShowing() == true

                // 判断处理逻辑：
                // 情况1：isShowPlayPage = true，先显示SongPlayController后进入VipPaymentFragment
                //       按返回键应该关闭VipPaymentFragment，保持SongPlayController显示
                // 情况2：isShowPlayPage = false，先进入VipPaymentFragment后显示SongPlayController
                //       如果SongPlayController正在显示，应该先关闭SongPlayController
                if (!isShowPlayPage && isSongPlayControllerShowing) {
                    // 情况2：先进入VipPaymentFragment，后显示SongPlayController，且SongPlayController正在显示
                    // 委托给MainActivity处理，先关闭SongPlayController
                    Log.d(TAG, "Case 2: VipPaymentFragment entered first, SongPlayController showing, delegating to MainActivity")
                    isEnabled = false
                    requireActivity().onBackPressedDispatcher.onBackPressed()
                    isEnabled = true
                } else {
                    // 情况1：先显示SongPlayController，后进入VipPaymentFragment
                    // 或者情况2但SongPlayController没有显示
                    // VipPaymentFragment自己处理返回事件
                    Log.d(TAG, "Case 1 or Case 2 without SongPlayController: VipPaymentFragment handling back press, isShowPlayPage=$isShowPlayPage, isSongPlayControllerShowing=$isSongPlayControllerShowing")
                    goBack()
                }
            }
        })
    }

}