package com.sgmw.ksongs.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import com.sgmw.common.BaseApplication
import com.sgmw.common.config.MemoryOptimizationConfig
import com.sgmw.common.utils.Log
import kotlinx.coroutines.*

/**
 * 内存监控工具类
 * 用于监控应用内存使用情况，预防OOM异常
 */
object MemoryMonitor {

    private const val TAG = "MemoryMonitor"
    // 使用配置文件中的参数
    private val MEMORY_WARNING_THRESHOLD = MemoryOptimizationConfig.MonitorConfig.MEMORY_WARNING_THRESHOLD
    private val MEMORY_CRITICAL_THRESHOLD = MemoryOptimizationConfig.MonitorConfig.MEMORY_CRITICAL_THRESHOLD
    private val MONITOR_INTERVAL = MemoryOptimizationConfig.MonitorConfig.MONITOR_INTERVAL_MS

    // 应用级别的协程作用域，用于管理所有内存相关的协程
    private var applicationScope: CoroutineScope? = null
    private var monitorJob: Job? = null
    private var isMonitoring = false

    // 添加节流控制，避免频繁的内存清理操作
    private var lastCleanupTime = 0L
    private var lastCriticalCleanupTime = 0L
    private var lastFinalizerCheckTime = 0L
    private var lastFinalizerWarningTime = 0L // 新增：终结器队列警告时间记录
    private const val CLEANUP_THROTTLE_INTERVAL = 30000L // 30秒内最多执行一次普通清理
    private const val CRITICAL_CLEANUP_THROTTLE_INTERVAL = 10000L // 10秒内最多执行一次关键清理
    private const val FINALIZER_CHECK_INTERVAL = 30000L // 优化：30秒检查一次终结器队列（从60秒降低）
    private const val FINALIZER_WARNING_THROTTLE = 60000L // 新增：终结器队列警告节流间隔

    // 终结器队列监控阈值优化
    private const val FINALIZER_QUEUE_WARNING_THRESHOLD = 300 // 优化：警告阈值从500降到300
    private const val FINALIZER_QUEUE_CRITICAL_THRESHOLD = 500 // 优化：关键阈值从1000降到500
    private const val FINALIZER_QUEUE_EMERGENCY_THRESHOLD = 800 // 新增：紧急阈值

    // 长时间运行监控参数（新增）
    private const val LONG_RUNNING_THRESHOLD_HOURS = 2 // 2小时后认为是长时间运行
    private const val MTBF_RUNNING_THRESHOLD_HOURS = 6 // 6小时后认为是MTBF测试场景
    private const val LONG_RUNNING_CLEANUP_INTERVAL = 1800000L // 30分钟执行一次长时间运行清理
    private const val MTBF_CLEANUP_INTERVAL = 900000L // 15分钟执行一次MTBF清理

    // 运行时长监控变量
    private val applicationStartTime = System.currentTimeMillis()
    private var lastLongRunningCleanupTime = 0L
    private var isMTBFMode = false
    
    /**
     * 初始化应用级别的协程作用域
     */
    private fun initApplicationScope() {
        if (applicationScope == null) {
            applicationScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
            Log.d(TAG, "Application scope initialized for memory monitoring")
        }
    }

    /**
     * 开始内存监控
     */
    fun startMonitoring() {
        if (!MemoryOptimizationConfig.MonitorConfig.ENABLE_MEMORY_MONITORING) {
            Log.d(TAG, "Memory monitoring is disabled in config")
            return
        }

        if (isMonitoring) {
            Log.d(TAG, "Memory monitoring is already running")
            return
        }

        // 初始化应用级别的协程作用域
        initApplicationScope()

        isMonitoring = true
        monitorJob = applicationScope?.launch {
            while (isMonitoring) {
                try {
                    checkMemoryUsage()
                    delay(MONITOR_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "Error during memory monitoring", e)
                }
            }
        }
        Log.d(TAG, "Memory monitoring started")
    }
    
    /**
     * 停止内存监控
     */
    fun stopMonitoring() {
        isMonitoring = false
        monitorJob?.cancel()
        monitorJob = null
        Log.d(TAG, "Memory monitoring stopped")
    }

    /**
     * 清理所有资源，在应用退出时调用
     */
    fun cleanup() {
        try {
            Log.d(TAG, "Cleaning up MemoryMonitor resources")
            stopMonitoring()

            // 取消应用级别的协程作用域
            applicationScope?.cancel()
            applicationScope = null

            Log.d(TAG, "MemoryMonitor cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during MemoryMonitor cleanup", e)
        }
    }

    /**
     * 记录详细的内存状态信息（新增）
     */
    private fun logDetailedMemoryStatus(memoryUsageRatio: Float, processMemInfo: Debug.MemoryInfo, systemMemInfo: ActivityManager.MemoryInfo) {
        try {
            val memoryUsagePercent = (memoryUsageRatio * 100).toInt()

            // 记录详细的进程内存分布
            Log.w(TAG, "Detailed Memory Analysis - Usage: $memoryUsagePercent%")
            Log.w(TAG, "Process Memory Breakdown:")
            Log.w(TAG, "  - Native Private: ${processMemInfo.nativePrivateDirty}KB")
            Log.w(TAG, "  - Native PSS: ${processMemInfo.nativePss}KB")
            Log.w(TAG, "  - Dalvik Private: ${processMemInfo.dalvikPrivateDirty}KB")
            Log.w(TAG, "  - Dalvik PSS: ${processMemInfo.dalvikPss}KB")
            Log.w(TAG, "  - Other Private: ${processMemInfo.otherPrivateDirty}KB")
            Log.w(TAG, "  - Other PSS: ${processMemInfo.otherPss}KB")
            Log.w(TAG, "  - Total PSS: ${processMemInfo.totalPss}KB")

            // 记录系统内存压力指标
            val systemMemoryPressure = (1.0f - systemMemInfo.availMem.toFloat() / systemMemInfo.totalMem.toFloat()) * 100
            Log.w(TAG, "System Memory Pressure: ${systemMemoryPressure.toInt()}%")

            // 记录内存压力等级
            val pressureLevel = when {
                memoryUsageRatio >= MEMORY_CRITICAL_THRESHOLD -> "CRITICAL"
                memoryUsageRatio >= MEMORY_WARNING_THRESHOLD -> "HIGH"
                else -> "NORMAL"
            }
            Log.w(TAG, "Memory Pressure Level: $pressureLevel")

            // 如果是关键状态，记录更多诊断信息
            if (memoryUsageRatio >= MEMORY_CRITICAL_THRESHOLD) {
                logCriticalMemoryDiagnostics()
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error logging detailed memory status", e)
        }
    }

    /**
     * 记录关键内存诊断信息（新增）
     */
    private fun logCriticalMemoryDiagnostics() {
        try {
            Log.e(TAG, "=== CRITICAL MEMORY DIAGNOSTICS ===")

            // 记录运行时内存信息
            val runtime = Runtime.getRuntime()
            Log.e(TAG, "Runtime Memory:")
            Log.e(TAG, "  - Max Memory: ${runtime.maxMemory() / 1024 / 1024}MB")
            Log.e(TAG, "  - Total Memory: ${runtime.totalMemory() / 1024 / 1024}MB")
            Log.e(TAG, "  - Free Memory: ${runtime.freeMemory() / 1024 / 1024}MB")
            Log.e(TAG, "  - Used Memory: ${(runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024}MB")

            // 记录终结器队列状态
            val finalizerQueueSize = getFinalizerQueueSize()
            Log.e(TAG, "Finalizer Queue Size: $finalizerQueueSize")

            // 记录线程数量
            val threadGroup = Thread.currentThread().threadGroup
            var rootGroup = threadGroup
            while (rootGroup?.parent != null) {
                rootGroup = rootGroup.parent
            }
            val threadCount = rootGroup?.activeCount() ?: 0
            Log.e(TAG, "Active Thread Count: $threadCount")

            Log.e(TAG, "=== END CRITICAL DIAGNOSTICS ===")

        } catch (e: Exception) {
            Log.e(TAG, "Error logging critical memory diagnostics", e)
        }
    }

    /**
     * 检查是否处于内存压力状态
     * 用于异常处理器中判断是否应该进行复杂操作
     */
    fun isMemoryPressureHigh(): Boolean {
        return try {
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()

            // 如果内存使用率超过警告阈值，认为处于内存压力状态
            memoryUsageRatio >= MEMORY_WARNING_THRESHOLD
        } catch (e: Exception) {
            // 如果检查内存状态时出现异常，保守地认为处于内存压力状态
            true
        }
    }
    
    /**
     * 检查内存使用情况
     */
    private fun checkMemoryUsage() {
        try {
            val context = BaseApplication.context
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            // 获取应用内存使用情况
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()

            // 获取详细内存信息
            val memoryInfo2 = Debug.MemoryInfo()
            Debug.getMemoryInfo(memoryInfo2)

            // 增强内存状态日志记录
            val memoryUsagePercent = (memoryUsageRatio * 100).toInt()
            val usedMemoryMB = usedMemory / 1024 / 1024
            val maxMemoryMB = maxMemory / 1024 / 1024

            Log.d(TAG, "Memory usage: $memoryUsagePercent% (${usedMemoryMB}MB / ${maxMemoryMB}MB)")
            Log.d(TAG, "Native heap: ${memoryInfo2.nativePrivateDirty}KB, " +
                    "Dalvik heap: ${memoryInfo2.dalvikPrivateDirty}KB")

            // 添加系统内存信息
            Log.d(TAG, "System memory - Available: ${memoryInfo.availMem / 1024 / 1024}MB, " +
                    "Total: ${memoryInfo.totalMem / 1024 / 1024}MB, LowMemory: ${memoryInfo.lowMemory}")

            // 当内存使用率较高时，记录更详细的信息
            if (memoryUsageRatio >= MEMORY_WARNING_THRESHOLD) {
                logDetailedMemoryStatus(memoryUsageRatio, memoryInfo2, memoryInfo)
            }

            // 检查终结器队列状态
            checkFinalizerQueue()

            // 检查长时间运行状态（新增）
            checkLongRunningStatus()

            // 增强的内存压力处理逻辑
            when {
                memoryUsageRatio >= MEMORY_CRITICAL_THRESHOLD -> {
                    Log.w(TAG, "Critical memory usage detected: ${(memoryUsageRatio * 100).toInt()}%")
                    handleCriticalMemoryPressure(memoryUsageRatio)
                }
                memoryUsageRatio >= MEMORY_WARNING_THRESHOLD -> {
                    Log.w(TAG, "High memory usage detected: ${(memoryUsageRatio * 100).toInt()}%")
                    handleHighMemoryPressure(memoryUsageRatio)
                }
                else -> {
                    // 正常状态下也要检查是否需要预防性清理
                    checkPreventiveCleanup(memoryUsageRatio)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error checking memory usage", e)
        }
    }
    
    /**
     * 处理关键内存压力状态（新增）
     */
    private fun handleCriticalMemoryPressure(memoryUsageRatio: Float) {
        try {
            Log.e(TAG, "Handling critical memory pressure: ${(memoryUsageRatio * 100).toInt()}%")

            // 立即检查终结器队列状态
            val finalizerQueueSize = getFinalizerQueueSize()
            if (finalizerQueueSize > FINALIZER_QUEUE_WARNING_THRESHOLD) {
                Log.e(TAG, "Critical memory pressure combined with high finalizer queue: $finalizerQueueSize")
                Log.e(TAG, "High risk of FinalizerWatchdogDaemon timeout!")

                // 触发紧急处理
                handleFinalizerEmergency(finalizerQueueSize)
            }

            // 执行关键内存清理
            performCriticalMemoryCleanup()

            // 记录关键状态，用于后续分析
            Log.e(TAG, "Critical memory pressure handled - monitoring for stability")

        } catch (e: Exception) {
            Log.e(TAG, "Error handling critical memory pressure", e)
        }
    }

    /**
     * 处理高内存压力状态（新增）
     */
    private fun handleHighMemoryPressure(memoryUsageRatio: Float) {
        try {
            Log.w(TAG, "Handling high memory pressure: ${(memoryUsageRatio * 100).toInt()}%")

            // 检查终结器队列状态，提前预警
            val finalizerQueueSize = getFinalizerQueueSize()
            if (finalizerQueueSize > FINALIZER_QUEUE_WARNING_THRESHOLD) {
                Log.w(TAG, "High memory pressure with elevated finalizer queue: $finalizerQueueSize")
                Log.w(TAG, "Proactive cleanup to prevent FinalizerDaemon overload")

                // 主动执行温和清理
                performGentleMemoryCleanup()
            }

            // 执行常规内存清理
            performMemoryCleanup()

        } catch (e: Exception) {
            Log.e(TAG, "Error handling high memory pressure", e)
        }
    }

    /**
     * 检查长时间运行状态（新增）
     */
    private fun checkLongRunningStatus() {
        try {
            val currentTime = System.currentTimeMillis()
            val runningTimeHours = (currentTime - applicationStartTime) / (1000 * 60 * 60)

            // 检查是否进入MTBF模式
            if (runningTimeHours >= MTBF_RUNNING_THRESHOLD_HOURS && !isMTBFMode) {
                isMTBFMode = true
                Log.w(TAG, "Entering MTBF mode - running for $runningTimeHours hours")
                Log.w(TAG, "Activating enhanced stability measures for long-term testing")

                // 立即执行一次MTBF清理
                performMTBFCleanup()
            }

            // 根据运行时长决定清理策略
            val cleanupInterval = if (isMTBFMode) MTBF_CLEANUP_INTERVAL else LONG_RUNNING_CLEANUP_INTERVAL
            val shouldPerformLongRunningCleanup = runningTimeHours >= LONG_RUNNING_THRESHOLD_HOURS &&
                    (currentTime - lastLongRunningCleanupTime) > cleanupInterval

            if (shouldPerformLongRunningCleanup) {
                Log.i(TAG, "Performing long-running cleanup - uptime: $runningTimeHours hours, MTBF mode: $isMTBFMode")
                lastLongRunningCleanupTime = currentTime

                if (isMTBFMode) {
                    performMTBFCleanup()
                } else {
                    performLongRunningCleanup()
                }
            }

            // 记录运行状态（每小时记录一次）
            if (runningTimeHours > 0 && runningTimeHours % 1 == 0L) {
                Log.i(TAG, "Application uptime: $runningTimeHours hours, MTBF mode: $isMTBFMode")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error checking long running status", e)
        }
    }

    /**
     * 执行长时间运行清理（新增）
     */
    private fun performLongRunningCleanup() {
        try {
            Log.i(TAG, "Performing long-running stability cleanup")

            // 执行温和的内存清理
            performGentleMemoryCleanup()

            // 同步MMKV数据，防止数据积累
            try {
                com.sgmw.common.utils.MMKVUtils.sync()
            } catch (e: Exception) {
                Log.e(TAG, "Error syncing MMKV during long-running cleanup", e)
            }

            // 检查终结器队列状态
            val finalizerQueueSize = getFinalizerQueueSize()
            if (finalizerQueueSize > FINALIZER_QUEUE_WARNING_THRESHOLD) {
                Log.w(TAG, "Long-running cleanup: Finalizer queue elevated: $finalizerQueueSize")
                // 不执行额外清理，避免过度干预
            }

            Log.i(TAG, "Long-running cleanup completed")

        } catch (e: Exception) {
            Log.e(TAG, "Error during long-running cleanup", e)
        }
    }

    /**
     * 执行MTBF测试清理（新增）
     */
    private fun performMTBFCleanup() {
        try {
            Log.w(TAG, "Performing MTBF stability cleanup")

            // MTBF模式下更积极的清理策略
            performCriticalMemoryCleanup()

            // 强制同步所有数据
            try {
                com.sgmw.common.utils.MMKVUtils.sync()
            } catch (e: Exception) {
                Log.e(TAG, "Error syncing MMKV during MTBF cleanup", e)
            }

            // 检查系统稳定性指标
            val finalizerQueueSize = getFinalizerQueueSize()
            val memoryUsage = getCurrentMemoryUsage()

            Log.w(TAG, "MTBF stability check - Finalizer queue: $finalizerQueueSize, Memory: ${(memoryUsage.usageRatio * 100).toInt()}%")

            // 如果终结器队列过大，记录警告
            if (finalizerQueueSize > FINALIZER_QUEUE_CRITICAL_THRESHOLD) {
                Log.e(TAG, "MTBF WARNING: High finalizer queue detected during long-term testing")
                Log.e(TAG, "This may indicate potential FinalizerWatchdogDaemon timeout risk in MTBF scenario")
            }

            Log.w(TAG, "MTBF cleanup completed")

        } catch (e: Exception) {
            Log.e(TAG, "Error during MTBF cleanup", e)
        }
    }

    /**
     * 检查是否需要预防性清理（新增）
     */
    private fun checkPreventiveCleanup(memoryUsageRatio: Float) {
        try {
            // 当内存使用率达到60%且终结器队列积压时，进行预防性清理
            if (memoryUsageRatio >= 0.6f) {
                val finalizerQueueSize = getFinalizerQueueSize()
                if (finalizerQueueSize > FINALIZER_QUEUE_WARNING_THRESHOLD) {
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastFinalizerWarningTime > FINALIZER_WARNING_THROTTLE) {
                        Log.i(TAG, "Preventive cleanup triggered - Memory: ${(memoryUsageRatio * 100).toInt()}%, Finalizer queue: $finalizerQueueSize")
                        lastFinalizerWarningTime = currentTime
                        performGentleMemoryCleanup()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking preventive cleanup", e)
        }
    }

    /**
     * 执行内存清理（温和版本，添加节流控制）
     */
    private fun performMemoryCleanup() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastCleanupTime < CLEANUP_THROTTLE_INTERVAL) {
            Log.d(TAG, "Memory cleanup skipped due to throttling")
            return
        }

        try {
            Log.d(TAG, "Performing memory cleanup")
            lastCleanupTime = currentTime

            // 清理图片缓存 - 使用应用级别的协程作用域，必须在主线程执行
            applicationScope?.launch(Dispatchers.Main) {
                try {
                    com.bumptech.glide.Glide.get(BaseApplication.context).clearMemory()
                    Log.d(TAG, "Glide memory cache cleared successfully")
                } catch (e: Exception) {
                    Log.e(TAG, "Error clearing Glide memory cache", e)
                }
            }

            // 移除强制垃圾回收，让系统自然回收
            // System.gc() // 注释掉强制GC，避免触发FinalizerDaemon过载

            Log.d(TAG, "Memory cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during memory cleanup", e)
        }
    }
    
    /**
     * 执行关键内存清理（优化版本，避免FinalizerDaemon过载）
     */
    private fun performCriticalMemoryCleanup() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastCriticalCleanupTime < CRITICAL_CLEANUP_THROTTLE_INTERVAL) {
            Log.d(TAG, "Critical memory cleanup skipped due to throttling")
            return
        }

        try {
            Log.w(TAG, "Performing critical memory cleanup")
            lastCriticalCleanupTime = currentTime

            // 检查终结器队列状态，决定清理策略
            val finalizerQueueSize = getFinalizerQueueSize()
            val isFinalizerEmergency = finalizerQueueSize > FINALIZER_QUEUE_CRITICAL_THRESHOLD

            if (isFinalizerEmergency) {
                Log.e(TAG, "Critical cleanup with finalizer emergency: $finalizerQueueSize")
                performFinalizerAwareCriticalCleanup(finalizerQueueSize)
            } else {
                performStandardCriticalCleanup()
            }

            Log.w(TAG, "Critical memory cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during critical memory cleanup", e)
        }
    }

    /**
     * 执行标准关键清理（新增）
     */
    private fun performStandardCriticalCleanup() {
        try {
            // 清理所有图片缓存 - 使用应用级别的协程作用域，必须在主线程执行
            applicationScope?.launch(Dispatchers.Main) {
                try {
                    val glide = com.bumptech.glide.Glide.get(BaseApplication.context)
                    glide.clearMemory()
                    Log.d(TAG, "Standard critical Glide memory cache cleared successfully")

                    // 在后台线程清理磁盘缓存，避免阻塞
                    applicationScope?.launch(Dispatchers.IO) {
                        try {
                            glide.clearDiskCache()
                            Log.d(TAG, "Standard critical Glide disk cache cleared successfully")
                        } catch (e: Exception) {
                            Log.e(TAG, "Error clearing Glide disk cache", e)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error clearing Glide caches", e)
                }
            }

            // 同步MMKV数据
            try {
                com.sgmw.common.utils.MMKVUtils.sync()
            } catch (e: Exception) {
                Log.e(TAG, "Error syncing MMKV", e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during standard critical cleanup", e)
        }
    }

    /**
     * 执行终结器感知的关键清理（新增）
     */
    private fun performFinalizerAwareCriticalCleanup(finalizerQueueSize: Int) {
        try {
            Log.e(TAG, "Performing finalizer-aware critical cleanup - queue size: $finalizerQueueSize")
            Log.e(TAG, "HIGH RISK: Potential FinalizerWatchdogDaemon timeout scenario detected!")

            // 立即清理所有可能的缓存
            applicationScope?.launch(Dispatchers.Main) {
                try {
                    val glide = com.bumptech.glide.Glide.get(BaseApplication.context)
                    glide.clearMemory()
                    Log.d(TAG, "Emergency Glide memory cache cleared")

                    // 立即清理磁盘缓存（在IO线程）
                    applicationScope?.launch(Dispatchers.IO) {
                        try {
                            glide.clearDiskCache()
                            Log.d(TAG, "Emergency Glide disk cache cleared")
                        } catch (e: Exception) {
                            Log.e(TAG, "Error clearing emergency Glide disk cache", e)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error clearing emergency Glide caches", e)
                }
            }

            // 强制同步MMKV数据，减少待终结化对象
            try {
                com.sgmw.common.utils.MMKVUtils.sync()
                Log.d(TAG, "MMKV force synced during finalizer emergency")
            } catch (e: Exception) {
                Log.e(TAG, "Error force syncing MMKV", e)
            }

            // 记录紧急状态信息，用于后续分析
            Log.e(TAG, "Finalizer-aware critical cleanup completed")
            Log.e(TAG, "Monitor for FinalizerWatchdogDaemon stability after cleanup")

        } catch (e: Exception) {
            Log.e(TAG, "Error during finalizer-aware critical cleanup", e)
        }
    }
    
    /**
     * 获取当前内存使用情况
     */
    fun getCurrentMemoryUsage(): MemoryUsageInfo {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()

        return MemoryUsageInfo(
            usedMemoryMB = usedMemory / 1024 / 1024,
            maxMemoryMB = maxMemory / 1024 / 1024,
            usageRatio = memoryUsageRatio
        )
    }

    /**
     * 获取应用运行时长（小时）（新增）
     */
    fun getApplicationUptimeHours(): Long {
        return (System.currentTimeMillis() - applicationStartTime) / (1000 * 60 * 60)
    }

    /**
     * 检查是否处于MTBF测试模式（新增）
     */
    fun isMTBFMode(): Boolean {
        return isMTBFMode
    }

    /**
     * 强制触发MTBF清理（新增）
     * 用于外部在检测到特定条件时主动调用
     */
    fun forceMTBFCleanup() {
        try {
            Log.w(TAG, "Force MTBF cleanup triggered externally")
            performMTBFCleanup()
        } catch (e: Exception) {
            Log.e(TAG, "Error during force MTBF cleanup", e)
        }
    }

    /**
     * 执行系统级健康检查（新增）
     * 综合检查系统资源状态，提前预警潜在问题
     */
    fun performSystemHealthCheck(): SystemHealthStatus {
        return try {
            Log.i(TAG, "Performing comprehensive system health check")

            val memoryUsage = getCurrentMemoryUsage()
            val finalizerStatus = getFinalizerQueueStatus()
            val uptimeHours = getApplicationUptimeHours()

            // 检查系统线程状态
            val threadStatus = checkSystemThreadHealth()

            // 综合评估系统健康状态
            val overallHealth = evaluateOverallSystemHealth(
                memoryUsage, finalizerStatus, threadStatus, uptimeHours
            )

            Log.i(TAG, "System health check completed - Status: ${overallHealth.status}")
            overallHealth

        } catch (e: Exception) {
            Log.e(TAG, "Error during system health check", e)
            SystemHealthStatus("ERROR", "Health check failed: ${e.message}", emptyList())
        }
    }

    /**
     * 检查系统线程健康状态（新增）
     */
    private fun checkSystemThreadHealth(): String {
        return try {
            val threadGroup = Thread.currentThread().threadGroup
            var rootGroup = threadGroup
            while (rootGroup?.parent != null) {
                rootGroup = rootGroup.parent
            }

            val threadCount = rootGroup?.activeCount() ?: 0
            val threads = arrayOfNulls<Thread>(threadCount)
            rootGroup?.enumerate(threads)

            var finalizerDaemonFound = false
            var finalizerWatchdogFound = false
            var suspiciousThreads = 0

            threads.forEach { thread ->
                thread?.let {
                    when {
                        it.name.contains("FinalizerDaemon") -> finalizerDaemonFound = true
                        it.name.contains("FinalizerWatchdogDaemon") -> finalizerWatchdogFound = true
                        it.state == Thread.State.BLOCKED || it.state == Thread.State.WAITING -> suspiciousThreads++
                    }
                }
            }

            when {
                !finalizerDaemonFound || !finalizerWatchdogFound -> "CRITICAL"
                suspiciousThreads > threadCount * 0.3 -> "WARNING"
                threadCount > 200 -> "WARNING"
                else -> "HEALTHY"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking system thread health", e)
            "UNKNOWN"
        }
    }

    /**
     * 综合评估系统健康状态（新增）
     */
    private fun evaluateOverallSystemHealth(
        memoryUsage: MemoryUsageInfo,
        finalizerStatus: FinalizerQueueStatus,
        threadStatus: String,
        uptimeHours: Long
    ): SystemHealthStatus {

        val warnings = mutableListOf<String>()

        // 内存状态评估
        if (memoryUsage.usageRatio >= MEMORY_CRITICAL_THRESHOLD) {
            warnings.add("Critical memory usage: ${(memoryUsage.usageRatio * 100).toInt()}%")
        } else if (memoryUsage.usageRatio >= MEMORY_WARNING_THRESHOLD) {
            warnings.add("High memory usage: ${(memoryUsage.usageRatio * 100).toInt()}%")
        }

        // 终结器队列状态评估
        if (finalizerStatus.isHighRisk) {
            warnings.add("High finalizer queue risk: ${finalizerStatus.queueSize} objects")
        }

        // 线程状态评估
        if (threadStatus == "CRITICAL") {
            warnings.add("Critical thread system issues detected")
        } else if (threadStatus == "WARNING") {
            warnings.add("Thread system performance concerns")
        }

        // 运行时长评估
        if (uptimeHours >= MTBF_RUNNING_THRESHOLD_HOURS) {
            warnings.add("Long-term operation: ${uptimeHours}h (MTBF mode)")
        } else if (uptimeHours >= LONG_RUNNING_THRESHOLD_HOURS) {
            warnings.add("Extended operation: ${uptimeHours}h")
        }

        // 确定整体状态
        val overallStatus = when {
            warnings.any { it.contains("Critical") } -> "CRITICAL"
            warnings.any { it.contains("High") || it.contains("performance") } -> "WARNING"
            warnings.isNotEmpty() -> "CAUTION"
            else -> "HEALTHY"
        }

        return SystemHealthStatus(overallStatus, "System health evaluation completed", warnings)
    }

    /**
     * 获取终结器队列状态信息（新增）
     * 用于外部监控和诊断
     */
    fun getFinalizerQueueStatus(): FinalizerQueueStatus {
        return try {
            val queueSize = getFinalizerQueueSize()
            val riskLevel = when {
                queueSize > FINALIZER_QUEUE_EMERGENCY_THRESHOLD -> "EMERGENCY"
                queueSize > FINALIZER_QUEUE_CRITICAL_THRESHOLD -> "CRITICAL"
                queueSize > FINALIZER_QUEUE_WARNING_THRESHOLD -> "WARNING"
                else -> "NORMAL"
            }

            FinalizerQueueStatus(
                queueSize = queueSize,
                riskLevel = riskLevel,
                isHighRisk = queueSize > FINALIZER_QUEUE_CRITICAL_THRESHOLD
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting finalizer queue status", e)
            FinalizerQueueStatus(0, "UNKNOWN", false)
        }
    }
    
    /**
     * 检查终结器队列状态，防止FinalizerDaemon线程过载（优化版本）
     * 增强监控和预警机制，提供更细粒度的处理策略
     */
    private fun checkFinalizerQueue() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastFinalizerCheckTime < FINALIZER_CHECK_INTERVAL) {
            return
        }

        try {
            lastFinalizerCheckTime = currentTime
            val finalizerQueueSize = getFinalizerQueueSize()

            // 记录详细的终结器队列状态
            logFinalizerQueueStatus(finalizerQueueSize)

            when {
                finalizerQueueSize > FINALIZER_QUEUE_EMERGENCY_THRESHOLD -> {
                    // 紧急状态：终结器队列严重积压，可能导致FinalizerWatchdogDaemon超时
                    Log.e(TAG, "EMERGENCY: Finalizer queue size is critically high: $finalizerQueueSize")
                    Log.e(TAG, "Risk of FinalizerWatchdogDaemon timeout detected!")
                    handleFinalizerEmergency(finalizerQueueSize)
                }
                finalizerQueueSize > FINALIZER_QUEUE_CRITICAL_THRESHOLD -> {
                    // 关键状态：需要积极清理
                    Log.w(TAG, "CRITICAL: Finalizer queue size is high: $finalizerQueueSize, triggering aggressive cleanup")
                    handleFinalizerCritical(finalizerQueueSize)
                }
                finalizerQueueSize > FINALIZER_QUEUE_WARNING_THRESHOLD -> {
                    // 警告状态：开始预警和温和清理
                    if (currentTime - lastFinalizerWarningTime > FINALIZER_WARNING_THROTTLE) {
                        Log.w(TAG, "WARNING: Finalizer queue size is elevated: $finalizerQueueSize, triggering gentle cleanup")
                        lastFinalizerWarningTime = currentTime
                        performGentleMemoryCleanup()
                    }
                }
                finalizerQueueSize > 100 -> {
                    // 正常监控状态
                    Log.d(TAG, "Finalizer queue size: $finalizerQueueSize (normal)")
                }
                else -> {
                    // 健康状态
                    Log.v(TAG, "Finalizer queue size: $finalizerQueueSize (healthy)")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking finalizer queue", e)
        }
    }

    /**
     * 记录终结器队列详细状态（新增）
     */
    private fun logFinalizerQueueStatus(queueSize: Int) {
        try {
            val currentMemoryUsage = getCurrentMemoryUsage()
            val memoryPressure = if (isMemoryPressureHigh()) "HIGH" else "NORMAL"

            // 每次检查都记录基本信息（使用DEBUG级别避免日志过多）
            Log.d(TAG, "FinalizerQueue Status - Size: $queueSize, Memory: ${currentMemoryUsage.usageRatio * 100}%, Pressure: $memoryPressure")

            // 当队列大小超过警告阈值时，记录更详细的信息
            if (queueSize > FINALIZER_QUEUE_WARNING_THRESHOLD) {
                Log.w(TAG, "FinalizerQueue Details - Queue: $queueSize, Used: ${currentMemoryUsage.usedMemoryMB}MB/${currentMemoryUsage.maxMemoryMB}MB")

                // 尝试获取更多系统信息
                logSystemMemoryStatus()

            }
        } catch (e: Exception) {
            Log.e(TAG, "Error logging finalizer queue status", e)
        }
    }

    /**
     * 处理终结器队列紧急状态（新增）
     */
    private fun handleFinalizerEmergency(queueSize: Int) {
        try {
            Log.e(TAG, "Handling finalizer emergency - queue size: $queueSize")

            // 立即执行紧急内存清理
            performEmergencyCleanup()

            // 记录紧急状态的详细信息，用于后续分析
            Log.e(TAG, "Emergency cleanup triggered due to finalizer queue overflow")
            Log.e(TAG, "This may indicate potential FinalizerWatchdogDaemon timeout risk")

            // 强制同步MMKV，减少待终结化的对象
            try {
                com.sgmw.common.utils.MMKVUtils.sync()
            } catch (e: Exception) {
                Log.e(TAG, "Error syncing MMKV during emergency", e)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error handling finalizer emergency", e)
        }
    }

    /**
     * 处理终结器队列关键状态（新增）
     */
    private fun handleFinalizerCritical(queueSize: Int) {
        try {
            Log.w(TAG, "Handling finalizer critical state - queue size: $queueSize")

            // 执行关键内存清理，但避免过于激进的操作
            performCriticalMemoryCleanup()

            // 记录关键状态信息
            Log.w(TAG, "Critical cleanup triggered - monitoring for FinalizerDaemon pressure")

        } catch (e: Exception) {
            Log.e(TAG, "Error handling finalizer critical state", e)
        }
    }

    /**
     * 获取终结器队列大小
     */
    private fun getFinalizerQueueSize(): Int {
        return try {
            val vmDebugClass = Class.forName("dalvik.system.VMDebug")
            val method = vmDebugClass.getMethod("countInstancesOfClass", Class::class.java, Boolean::class.javaPrimitiveType)
            val finalizerClass = Class.forName("java.lang.ref.FinalizerReference")
            method.invoke(null, finalizerClass, false) as Int
        } catch (e: Exception) {
            // 如果无法获取，返回0，不影响正常流程
            0
        }
    }

    /**
     * 执行温和的内存清理，不触发强制GC（优化版本）
     */
    private fun performGentleMemoryCleanup() {
        try {
            Log.d(TAG, "Performing gentle memory cleanup")

            // 检查终结器队列状态，决定清理积极程度
            val finalizerQueueSize = getFinalizerQueueSize()
            val isFinalizerPressureHigh = finalizerQueueSize > FINALIZER_QUEUE_WARNING_THRESHOLD

            if (isFinalizerPressureHigh) {
                Log.w(TAG, "Gentle cleanup with finalizer pressure: $finalizerQueueSize")
                // 终结器队列压力高时，执行更积极的清理
                performEnhancedGentleCleanup(finalizerQueueSize)
            } else {
                // 正常的温和清理
                performStandardGentleCleanup()
            }

            Log.d(TAG, "Gentle memory cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during gentle memory cleanup", e)
        }
    }

    /**
     * 执行标准温和清理（新增）
     */
    private fun performStandardGentleCleanup() {
        try {
            // 只清理图片内存缓存，不清理磁盘缓存 - 使用应用级别的协程作用域，必须在主线程执行
            applicationScope?.launch(Dispatchers.Main) {
                try {
                    com.bumptech.glide.Glide.get(BaseApplication.context).clearMemory()
                    Log.d(TAG, "Standard gentle Glide memory cache cleared successfully")
                } catch (e: Exception) {
                    Log.e(TAG, "Error clearing Glide memory cache", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during standard gentle cleanup", e)
        }
    }

    /**
     * 执行增强温和清理（新增）
     */
    private fun performEnhancedGentleCleanup(finalizerQueueSize: Int) {
        try {
            Log.w(TAG, "Performing enhanced gentle cleanup due to finalizer pressure: $finalizerQueueSize")

            // 清理图片缓存 - 使用应用级别的协程作用域，必须在主线程执行
            applicationScope?.launch(Dispatchers.Main) {
                try {
                    com.bumptech.glide.Glide.get(BaseApplication.context).clearMemory()
                    Log.d(TAG, "Enhanced gentle Glide memory cache cleared successfully")
                } catch (e: Exception) {
                    Log.e(TAG, "Error clearing Glide memory cache", e)
                }
            }

            // 同步MMKV数据，减少待终结化的对象
            try {
                com.sgmw.common.utils.MMKVUtils.sync()
                Log.d(TAG, "MMKV synced during enhanced gentle cleanup")
            } catch (e: Exception) {
                Log.e(TAG, "Error syncing MMKV during enhanced cleanup", e)
            }

            // 记录增强清理的原因
            Log.w(TAG, "Enhanced gentle cleanup completed - finalizer queue was: $finalizerQueueSize")

        } catch (e: Exception) {
            Log.e(TAG, "Error during enhanced gentle cleanup", e)
        }
    }

    /**
     * 记录系统内存状态（新增）
     */
    private fun logSystemMemoryStatus() {
        try {
            val context = BaseApplication.context
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            // 记录系统级内存信息
            Log.w(TAG, "System Memory - Available: ${memoryInfo.availMem / 1024 / 1024}MB, " +
                    "Total: ${memoryInfo.totalMem / 1024 / 1024}MB, " +
                    "LowMemory: ${memoryInfo.lowMemory}")

            // 记录详细的内存分布
            val memoryInfo2 = Debug.MemoryInfo()
            Debug.getMemoryInfo(memoryInfo2)
            Log.w(TAG, "Process Memory - Native: ${memoryInfo2.nativePrivateDirty}KB, " +
                    "Dalvik: ${memoryInfo2.dalvikPrivateDirty}KB, " +
                    "Other: ${memoryInfo2.otherPrivateDirty}KB")

        } catch (e: Exception) {
            Log.e(TAG, "Error logging system memory status", e)
        }
    }

    /**
     * 紧急内存清理，用于异常处理器调用
     */
    fun performEmergencyCleanup() {
        try {
            Log.w(TAG, "Performing emergency memory cleanup")

            // 立即清理图片缓存 - 检查是否在主线程
            try {
                if (android.os.Looper.myLooper() == android.os.Looper.getMainLooper()) {
                    // 在主线程，直接清理
                    com.bumptech.glide.Glide.get(BaseApplication.context).clearMemory()
                } else {
                    // 不在主线程，切换到主线程执行
                    android.os.Handler(android.os.Looper.getMainLooper()).post {
                        try {
                            com.bumptech.glide.Glide.get(BaseApplication.context).clearMemory()
                        } catch (e: Exception) {
                            Log.e(TAG, "Emergency cleanup: Error clearing Glide cache on main thread: $e")
                        }
                    }
                }
            } catch (e: Exception) {
                // 紧急情况下，异常也要静默处理
                Log.e(TAG, "Emergency cleanup: Error clearing Glide cache: $e")
            }

            // 同步MMKV数据
            try {
                com.sgmw.common.utils.MMKVUtils.sync()
            } catch (e: Exception) {
                Log.e(TAG, "Emergency cleanup: Error syncing MMKV: $e")
            }

            Log.w(TAG, "Emergency memory cleanup completed")
        } catch (e: Exception) {
            // 紧急清理失败也要静默处理，避免异常传播
            Log.e(TAG, "Error during emergency memory cleanup: $e")
        }
    }

    /**
     * 内存使用信息数据类
     */
    data class MemoryUsageInfo(
        val usedMemoryMB: Long,
        val maxMemoryMB: Long,
        val usageRatio: Float
    )

    /**
     * 终结器队列状态信息数据类（新增）
     */
    data class FinalizerQueueStatus(
        val queueSize: Int,
        val riskLevel: String,
        val isHighRisk: Boolean
    )

    /**
     * 系统健康状态信息数据类（新增）
     */
    data class SystemHealthStatus(
        val status: String,
        val message: String,
        val warnings: List<String>
    )
}
