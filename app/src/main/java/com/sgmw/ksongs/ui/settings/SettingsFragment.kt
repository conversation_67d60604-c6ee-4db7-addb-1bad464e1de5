package com.sgmw.ksongs.ui.settings

import androidx.navigation.fragment.findNavController
import com.blankj.utilcode.util.ToastUtils
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.ksongs.BuildConfig
import com.sgmw.ksongs.R
import com.sgmw.ksongs.constant.MMKVConstant
import com.sgmw.ksongs.databinding.FragmentSettingsBinding
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.dialog.ClearCacheDialogFragment
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.utils.CacheUtils
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.utils.showToast
import com.sgmw.ksongs.viewmodel.settings.SettingsViewModel
import com.tme.ktv.audio.model.SongInfoModel
import ksong.support.player.KtvPlayerConfig
import java.io.File

/**
 * 通用设置页面
 */

class SettingsFragment : BaseFrameFragment<FragmentSettingsBinding, SettingsViewModel>() {

    private var isGoBuyVip = false

    override fun onResume() {
        super.onResume()
        if (isGoBuyVip) {
            isGoBuyVip = false
            mViewModel?.getVipInfo()
        }
    }

    override fun FragmentSettingsBinding.initView() {
        Log.d(TAG, "initView")
        mBinding?.let {
            it.ivBack.setOnSingleClickListener {
                findNavController().popBackStack()
            }
            val videoQuality = KtvPlayerConfig.getInstance().videoQuality
            Log.d(TAG, "initView videoQuality = $videoQuality")
            when (videoQuality) {
                SongInfoModel.MV_480P -> {
                    it.rbStandard.isChecked = true
                }

                SongInfoModel.MV_720P -> {
                    it.rbHigh.isChecked = true
                }

                SongInfoModel.MV_1080P -> {
                    if (MMKVUtils.getBoolean(MMKVConstant.IS_VIP, false)) {
                        it.rbSuper.isChecked = true
                    } else {
                        it.rbHigh.isChecked = true
                        KtvPlayerConfig.getInstance().setVideoQuality(SongInfoModel.MV_720P)
                    }
                }
            }
            it.rpDefinition.setOnCheckedChangeListener { _, i ->
                when (i) {
                    R.id.rb_standard -> {
                        KtvPlayerConfig.getInstance().setVideoQuality(SongInfoModel.MV_480P)
                        SensorsDataManager.trackClickEvent(
                            BigDataConstants.EVENT_CODE_WESING_MV_QUALITY_SWITCH,
                            BigDataConstants.EVENT_NAME_WESING_MV_QUALITY_SWITCH,
                            newpPropertiesInfo = mapOf(
                                BigDataConstants.CARD_NAME to getString(R.string.standard_definition),
                            )
                        )
                    }

                    R.id.rb_high -> {
                        KtvPlayerConfig.getInstance().setVideoQuality(SongInfoModel.MV_720P)
                        SensorsDataManager.trackClickEvent(
                            BigDataConstants.EVENT_CODE_WESING_MV_QUALITY_SWITCH,
                            BigDataConstants.EVENT_NAME_WESING_MV_QUALITY_SWITCH,
                            newpPropertiesInfo = mapOf(
                                BigDataConstants.CARD_NAME to getString(R.string.high_definition),
                            )
                        )
                    }

                    R.id.rb_super ->
                        if (MMKVUtils.getBoolean(MMKVConstant.IS_VIP, false)) {
                            KtvPlayerConfig.getInstance().setVideoQuality(SongInfoModel.MV_1080P)
                            SensorsDataManager.trackClickEvent(
                                BigDataConstants.EVENT_CODE_WESING_MV_QUALITY_SWITCH,
                                BigDataConstants.EVENT_NAME_WESING_MV_QUALITY_SWITCH,
                                newpPropertiesInfo = mapOf(
                                    BigDataConstants.CARD_NAME to getString(R.string.super_definition),
                                )
                            )
                        } else {
                            if (KtvPlayerConfig.getInstance().videoQuality == SongInfoModel.MV_480P) {
                                it.rbStandard.isChecked = true
                            } else if (KtvPlayerConfig.getInstance().videoQuality == SongInfoModel.MV_720P) {
                                it.rbHigh.isChecked = true
                            }
                            isGoBuyVip = true
                            NavigationUtils.navigateSafely(findNavController(), R.id.action_settings_to_vip_payment)
                        }
                }
            }
            it.tvVersion.text = "V ${BuildConfig.VERSION_NAME}"
            val file = File(SettingsViewModel.cacheDirPath)
            val folderSize = CacheUtils.getFolderSize(file)
            val cacheSize = CacheUtils.byteToFileSize(folderSize)
            it.tvCache.text = cacheSize
            it.btnClearCache.setOnSingleClickListener {
                val isPlaying = KaraokePlayerManager.isPlaying()
                if (isPlaying) {
                    ToastUtils.showLong(R.string.playing_cache_toast)
                } else {
                    val cacheSizeStr = tvCache.text.toString()
                    if (cacheSizeStr == "0MB") {
                        showToast(R.string.no_cache_toast)
                    } else {
                        // 检查 LoginDialogFragment 是否已经显示
                        val fragment =
                            requireActivity().supportFragmentManager.findFragmentByTag("ClearCacheDialogFragment")
                        if (fragment == null) {
                            val clearCacheDialogFragment = ClearCacheDialogFragment()
                            clearCacheDialogFragment.setOnClearCacheListener(object :
                                ClearCacheDialogFragment.OnClearCacheListener {
                                override fun onSuccess() {
                                    mBinding?.tvCache?.text = "0MB"
                                    showToast(R.string.clear_cahche_success)
                                }

                                override fun onFailed() {
                                    showToast(R.string.clear_cache_failed)
                                }
                            })
                            // 弹出 LoginDialogFragment
                            clearCacheDialogFragment.show(
                                requireActivity().supportFragmentManager,
                                "ClearCacheDialogFragment"
                            )
                        }
                    }
                }
                SensorsDataManager.trackClickEvent(
                    BigDataConstants.EVENT_CODE_WESING_CACHE_CLEAR,
                    BigDataConstants.EVENT_NAME_WESING_CACHE_CLEAR,
                    newpPropertiesInfo = null
                )
            }
            it.llMic.setOnSingleClickListener {
                NavigationUtils.navigateSafely(findNavController(), R.id.action_settings_to_mic_connection_guide)
            }
            it.llPrivacyService.setOnSingleClickListener {
                NavigationUtils.navigateSafely(findNavController(), R.id.action_settings_to_protocol)
            }
        }
    }

    override fun initObserve() {
        mViewModel?.apply {
            mVipInfoBean.observe(viewLifecycleOwner) {
                if (it != null) {
                    if (MMKVUtils.getBoolean(MMKVConstant.IS_VIP, false)) {
                        mBinding?.rbSuper?.isChecked = true
                    }
                }
            }
        }
    }

    override fun initRequestData() {

    }

    companion object {
        private const val TAG = "SettingsFragment"
    }

}