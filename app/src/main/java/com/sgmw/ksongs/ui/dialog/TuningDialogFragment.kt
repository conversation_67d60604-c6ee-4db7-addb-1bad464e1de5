package com.sgmw.ksongs.ui.dialog

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.DialogTuningBinding
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.dialog.ScoreDialogFragment.Companion
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager

/**
 * @author: 董俊帅
 * @time: 2025/3/1
 * @desc: 调音弹窗
 */

class TuningDialogFragment : BaseBlurDialogFragment(R.layout.dialog_tuning) {

    private var beforeTuningPitchShift = ""
    private var beforeAccompanyVolume = ""
    private var beforeMicVolume = ""

    private val mBinding: DialogTuningBinding by lazy {
        DialogTuningBinding.inflate(layoutInflater)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // 设置点击外部关闭
        isCancelable = true
        dialog?.setCanceledOnTouchOutside(true)
        return mBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
    }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        mBinding.dialogContent.setOnSingleClickListener {}
        mBinding.root.setOnSingleClickListener { dismiss() }
        mBinding.ivBack.setOnSingleClickListener { dismiss() }
        mBinding.seekBarTuning.setOnSeekBarChangeListener(object :
            SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                seekBar?.let {
                    Log.d(TAG, "seekBarTuning onProgressChanged: $progress")
                    mBinding.tvTuningMax.text = progress.toString()
                    KaraokePlayerManager.setTonePitchShift(progress)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {

            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {

            }

        })

        mBinding.seekBarAccompany.setOnSeekBarChangeListener(object :
            SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                Log.d(TAG, "seekBarAccompany onProgressChanged: $progress")
                KaraokePlayerManager.setAccompanyVolume(progress)
                mBinding.tvAccompanyMax.text = progress.toString()
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {

            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {

            }

        })

        mBinding.seekBarMic.setOnSeekBarChangeListener(object :
            SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                Log.d(TAG, "seekBarMic onProgressChanged: $progress")
                KaraokePlayerManager.setMicVolume(progress)
                mBinding.tvMicMax.text = progress.toString()

            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {

            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {

            }
        })

        KaraokePlayerManager.getMicVolume().let {
            mBinding.seekBarMic.progress = it
            beforeMicVolume = it.toString()
            Log.d(TAG, "beforeMicVolume: $beforeMicVolume")
        }

        KaraokePlayerManager.getAccompanyVolume().let {
            mBinding.seekBarAccompany.progress = it
            beforeAccompanyVolume = it.toString()
            Log.d(TAG, "beforeAccompanyVolume: $beforeAccompanyVolume")
        }

        KaraokePlayerManager.getTonePitchShift().let {
            mBinding.seekBarTuning.progress = it
            beforeTuningPitchShift = it.toString()
            Log.d(TAG, "beforeTuningPitchShift: $beforeTuningPitchShift")
        }
    }

    override fun onDestroyView() {
        val newpPropertiesInfo = mutableMapOf<String, String>()
        if (!TextUtils.equals(beforeMicVolume, mBinding.tvMicMax.text.toString())) {
            newpPropertiesInfo[BigDataConstants.SET_VALUE] = mBinding.tvMicMax.text.toString()
        }
        if (!TextUtils.equals(beforeAccompanyVolume, mBinding.tvAccompanyMax.text.toString())) {
            newpPropertiesInfo[BigDataConstants.SET_VALUE_2] = mBinding.tvAccompanyMax.text.toString()
        }
        if (!TextUtils.equals(beforeTuningPitchShift, mBinding.tvTuningMax.text.toString())) {
            newpPropertiesInfo[BigDataConstants.CARD_NAME] = "升降调|" + mBinding.tvTuningMax.text.toString()
        }
        SensorsDataManager.trackClickEvent(
            BigDataConstants.EVENT_CODE_WESING_MIXING_CONSOLE_SET,
            BigDataConstants.EVENT_NAME_WESING_MIXING_CONSOLE_SET,
            newpPropertiesInfo = newpPropertiesInfo.toMap()
        )
        super.onDestroyView()
    }

    companion object {
        private const val TAG = "TuningDialogFragment"
    }

}