package com.sgmw.ksongs.ui.songplay

import androidx.lifecycle.MutableLiveData
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.model.bean.SongInfoBean
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

/**
 * @author: 董俊帅
 * @time: 2025/3/1
 * @desc: 播放器的一些状态及控制
 */
object KaraokeConsole {

    // 用于发送事件的协程作用域
    private val consoleScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)

    /**
     * 播放的错误信息
     */
    var currPlayError = MutableLiveData<Int>()

    /**
     * 当前播放的歌曲信息
     */
    var currSongInfo: SongInfoBean? = null
    var currSongInfoLiveData = MutableLiveData<SongInfoBean?>()

    /**
     * 当前播放到位置 - 使用 SharedFlow 替代 AutoCleanLiveData，避免粘性问题
     * replay = 0 确保新的观察者不会收到历史值,原理是每次发送新数据，SharedFlow仅会给已经订阅的观察者发送数据，且内部不缓存任何数据
     */
    private val _currentTime = MutableSharedFlow<Long>(replay = 0)
    val currentTime: SharedFlow<Long> = _currentTime.asSharedFlow()

    /**
     * 当前播放歌曲的总时长
     */
    val duration = MutableLiveData<Long>()

    /**
     * 预加载的歌曲进度 - 使用 SharedFlow 替代 AutoCleanLiveData，避免粘性问题
     * replay = 0 确保新的观察者不会收到历史值,原理是每次发送新数据，SharedFlow仅会给已经订阅的观察者发送数据，且内部不缓存任何数据
     */
    private val _preLoadProgress = MutableSharedFlow<Int>(replay = 1)
    val preLoadProgress: SharedFlow<Int> = _preLoadProgress.asSharedFlow()

    /**
     * 发送当前播放位置，供内部使用
     * 使用协程确保发送成功，即使没有立即的收集者
     */
    fun emitCurrentTime(time: Long) {
        consoleScope.launch {
            _currentTime.emit(time)
        }
    }

    /**
     * 发送预加载进度，供内部使用
     * 使用协程确保发送成功，即使没有立即的收集者
     */
    fun emitPreLoadProgress(progress: Int) {
        consoleScope.launch {
            Log.d("DJS" , "emitPreLoadProgress $progress")
            _preLoadProgress.emit(progress)
        }
    }

    /**
     * 视频播放失败或无视频，需要加载图片，此处会回调歌曲图片url
     */
    val shouldShowPicUrl = MutableLiveData<String>()

    /**
     * 播放状态
     * 使用的VideoState中的值
     */
    val playState = MutableLiveData<Int>()

    /**
     * 打分的分数
     */
    var mScore = MutableLiveData<String>()

    /**
     *  打分等级
     */
    var mLevel = MutableLiveData<String>()

    /**
     * MV开关
     */
    val isMvOpen = MutableLiveData<Boolean>()

    /**
     * 音准器开关
     */
    val isPitcherOpen = MutableLiveData<Boolean>()

    /**
     * 歌曲切换状态标志
     * 用于解决切换歌曲时lyricDotView异常显示的问题
     * true: 正在切换歌曲（包括重播、下一曲、从列表选择等）
     * false: 歌曲切换完成，允许正常显示lyricDotView
     */
    val isSongSwitching = MutableLiveData(false)

    /**
     * 设置歌曲切换状态
     * @param switching true表示开始切换歌曲，false表示切换完成
     */
    fun setSongSwitching(switching: Boolean) {
        isSongSwitching.postValue(switching)
        Log.d("KaraokeConsole", "setSongSwitching: $switching")
    }

}