# SGMW全民K歌项目背景

全民K歌应用基于KTV SDK开发，通过集成腾讯K歌SDK提供完整的KTV功能。应用与车载系统深度集成，为用户提供车内娱乐K歌体验。

在KTV SDK正常初始化后，全民K歌应用与SDK进行歌曲点播、播放控制、状态同步等指令和数据交互。全民K歌应用通过SDK获取歌曲资源、控制播放状态、获取打分信息等核心功能。歌曲点播功能通过网络与KTV服务器进行数据交互，获取歌曲列表、歌曲资源等信息。应用内建数据库用于存储本地播放列表、已唱歌曲记录、用户收藏等信息。

全民K歌的歌曲点播和播放记录数据通过KTV SDK协议与KTV服务器进行数据交互后，SDK中下载歌曲信息和用户数据到应用数据库，全民K歌应用从数据库获取相关歌曲和用户数据。在应用开关开启和歌曲资源判断使用SDK中封装的模块进行数据监听回调。

## 1. 系统架构

全民K歌应用采用模块化架构设计，主要包括以下几个核心模块：

### 1.1 核心模块
- **app模块**：主应用模块，包含主要业务逻辑和UI界面
- **common模块**：公共模块，提供通用工具和基础类
- **skin模块**：皮肤模块，包含皮肤适配和资源加载功能

### 1.2 技术架构
- **KTV SDK**：提供核心的K歌功能，包括歌曲点播、播放控制、打分系统等
- **Android Framework**：提供系统级功能支持，如音频焦点管理、Car API等
- **第三方库**：包括Glide图片加载、MMKV数据存储、SmartRefreshLayout下拉刷新等

## 2. 核心功能交互流程

### 2.1 歌曲点播流程
1. 用户在UI界面选择歌曲
2. 应用调用KTV SDK点播接口
3. SDK与服务器通信获取歌曲资源
4. SDK开始下载并缓存歌曲资源
5. 应用监听SDK播放状态并更新UI

### 2.2 播放控制流程
1. 用户操作播放控制按钮
2. 应用调用SDK播放控制接口
3. SDK控制音频播放并回调状态变化
4. 应用监听状态变化并更新UI

### 2.3 数据存储流程
1. SDK提供歌曲信息和用户数据
2. 应用将数据存储到本地数据库
3. 应用从数据库读取数据用于UI展示

## 3. 关键技术组件

### 3.1 KTV SDK集成
全民K歌应用基于腾讯KTV SDK开发，SDK提供以下核心功能：
- 歌曲点播和播放控制
- MV视频播放
- 实时打分系统
- 歌词显示和同步
- 音效调节

### 3.2 音频系统集成
应用与Android音频系统深度集成：
- 使用AudioFocus机制管理音频焦点
- 与Car API集成实现车机音频控制
- 支持多种音频输出设备

### 3.3 数据管理
- 本地数据库存储播放列表和用户数据
- MMKV用于轻量级数据存储
- SDK缓存机制管理歌曲资源

## 4. 状态管理

应用遵循统一的状态管理模式：
- 通过LiveData/Observable进行状态监听
- 集中管理播放状态、列表状态等核心数据
- 事件与状态分离，避免状态混乱

## 5. 第三方服务集成

### 5.1 登录系统
- 集成第三方登录服务
- 管理用户token和登录状态

### 5.2 数据统计
- 集成神策数据统计SDK
- 收集用户行为和使用数据

### 5.3 崩溃收集
- 集成FireEye和自定义崩溃收集机制
- 监控和上报应用异常信息